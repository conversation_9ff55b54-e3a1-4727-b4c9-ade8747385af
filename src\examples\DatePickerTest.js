import React, { useState } from 'react';
import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

// Componente de teste para verificar o DatePicker
const DatePickerTest = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());

  // Adiciona estilos de teste
  React.useEffect(() => {
    const styleId = "datepicker-test-styles";
    if (!document.getElementById(styleId)) {
      const styles = `
        .test-container {
          padding: 20px;
          max-width: 800px;
          margin: 0 auto;
          font-family: Arial, sans-serif;
        }
        
        .test-row {
          display: flex;
          margin-bottom: 20px;
          border: 1px solid #ddd;
          padding: 15px;
          border-radius: 5px;
        }
        
        .test-col {
          flex: 1;
          margin-right: 15px;
          overflow: visible;
        }
        
        .test-col:last-child {
          margin-right: 0;
        }
        
        .test-label {
          display: block;
          margin-bottom: 5px;
          font-weight: bold;
          color: #333;
        }
        
        .test-input {
          width: 100%;
          height: 38px;
          padding: 0.375rem 0.75rem;
          font-size: 0.875rem;
          line-height: 1.5;
          border: 1px solid #ced4da;
          border-radius: 0.375rem;
          box-sizing: border-box;
        }
        
        /* Estilos específicos para o DatePicker */
        .react-datepicker-wrapper {
          width: 100% !important;
        }
        
        .react-datepicker__input-container {
          width: 100% !important;
        }
        
        .react-datepicker__input-container input {
          width: 100% !important;
          height: 38px !important;
          padding: 0.375rem 0.75rem !important;
          font-size: 0.875rem !important;
          line-height: 1.5 !important;
          border: 1px solid #ced4da !important;
          border-radius: 0.375rem !important;
          box-sizing: border-box !important;
          overflow: visible !important;
          text-overflow: clip !important;
          white-space: nowrap !important;
          min-width: 0 !important;
        }
        
        .react-datepicker__input-container input:focus {
          border-color: #80bdff !important;
          outline: 0 !important;
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
        }
        
        .custom-datepicker-popper {
          z-index: 9999 !important;
        }
        
        .custom-datepicker-calendar {
          border: 1px solid #ced4da;
          border-radius: 0.375rem;
          box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
          font-family: inherit;
        }
        
        .custom-datepicker-calendar .react-datepicker__header {
          background-color: #007bff;
          border-bottom: 1px solid #007bff;
          border-radius: 0.375rem 0.375rem 0 0;
        }
        
        .custom-datepicker-calendar .react-datepicker__current-month {
          color: white;
          font-weight: 500;
        }
        
        .custom-datepicker-calendar .react-datepicker__day-name {
          color: white;
          font-weight: 500;
        }
        
        .custom-datepicker-calendar .react-datepicker__day:hover {
          background-color: #e9ecef;
          border-radius: 0.25rem;
        }
        
        .custom-datepicker-calendar .react-datepicker__day--selected {
          background-color: #007bff;
          color: white;
          border-radius: 0.25rem;
        }
        
        .custom-datepicker-calendar .react-datepicker__day--today {
          background-color: #ffc107;
          color: #212529;
          border-radius: 0.25rem;
          font-weight: 500;
        }
      `;
      
      const styleSheet = document.createElement("style");
      styleSheet.id = styleId;
      styleSheet.innerText = styles;
      document.head.appendChild(styleSheet);
    }
  }, []);

  return (
    <div className="test-container">
      <h2>Teste do DatePicker - Verificação de Corte</h2>
      
      <div className="test-row">
        <div className="test-col">
          <label className="test-label">Campo Normal:</label>
          <input 
            type="text" 
            className="test-input" 
            placeholder="Campo de texto normal"
            defaultValue="Texto de exemplo"
          />
        </div>
        
        <div className="test-col">
          <label className="test-label">Data Base:</label>
          <ReactDatePicker
            selected={selectedDate}
            onChange={(date) => setSelectedDate(date)}
            dateFormat="dd/MM/yyyy"
            placeholderText="Selecione uma data"
            showPopperArrow={false}
            popperClassName="custom-datepicker-popper"
            calendarClassName="custom-datepicker-calendar"
            onKeyDown={(e) => e.preventDefault()}
            minDate={new Date()}
          />
        </div>
        
        <div className="test-col">
          <label className="test-label">Outro Campo:</label>
          <input 
            type="text" 
            className="test-input" 
            placeholder="Outro campo"
            defaultValue="0,00"
          />
        </div>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
        <h4>Verificações:</h4>
        <ul>
          <li>✅ O campo DatePicker deve ter a mesma altura dos outros campos</li>
          <li>✅ O texto da data não deve ser cortado</li>
          <li>✅ O campo deve ocupar toda a largura disponível</li>
          <li>✅ O calendário deve aparecer completamente quando clicado</li>
          <li>✅ O foco deve funcionar corretamente</li>
        </ul>
        
        <p><strong>Data selecionada:</strong> {selectedDate ? selectedDate.toLocaleDateString('pt-BR') : 'Nenhuma'}</p>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#d4edda', borderRadius: '5px' }}>
        <h4>Soluções aplicadas:</h4>
        <ul>
          <li><code>width: 100% !important</code> - Garante largura total</li>
          <li><code>overflow: visible !important</code> - Evita corte do texto</li>
          <li><code>text-overflow: clip !important</code> - Remove reticências</li>
          <li><code>white-space: nowrap !important</code> - Evita quebra de linha</li>
          <li><code>min-width: 0 !important</code> - Remove largura mínima restritiva</li>
          <li><code>box-sizing: border-box !important</code> - Inclui padding na largura</li>
        </ul>
      </div>
    </div>
  );
};

export default DatePickerTest;
