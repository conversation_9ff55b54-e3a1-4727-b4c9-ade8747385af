import React, { useCallback, useEffect, useState } from "react";
import {
  CButton,
  CCard,
  CCardBody,
  CCol,
  CInput,
  CLabel,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CRow,
} from "@coreui/react";
import { TableDatacobTermo } from "./InstallmentTablesHandler";
import { getApi } from "src/reusable/functions";

const TermoJuridicoModal = ({ isOpen, onClose, datacobData }) => {
  const handleClose = () => {
    onClose();
  };

  const [tableData, setTableData] = useState(
    datacobData.flatMap((x) => x.parcelas)
  );

  const handleTableDataChange = (tableData) => {
    setTableData(tableData);
  };

  const [tipoTermo, setTipoTermo] = useState([]);
  const getTipoTermo = useCallback(async () => {
    try {
      const response = await getApi({}, "getTermoJuridico");
      if (response && response.length > 0) {
        setTipoTermo(response);
      } else {
        setTipoTermo([]);
      }
    } catch (error) {
      console.log(error);
    }
  }, []);

  useEffect(() => {
    getTipoTermo();
  }, [getTipoTermo]);

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={true}
      className="custom-modal modal-xxl"
      centered
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Dados para solicitar Termos Jurídicos:</h5>
      </CModalHeader>
      <CModalBody>
        <CRow className="mb-3">
          {tipoTermo.map((termo, index) => (
            <>
              <div>
                <div class="form-check form-check-inline">
                  <input
                    class="form-check-input"
                    type="radio"
                    name="inlineRadioOptions"
                    id="inlineRadio1"
                    value="option1"
                  />
                  <label class="form-check-label" for="inlineRadio1">
                    1
                  </label>
                </div>
                <div class="form-check form-check-inline">
                  <input
                    class="form-check-input"
                    type="radio"
                    name="inlineRadioOptions"
                    id="inlineRadio2"
                    value="option2"
                  />
                  <label class="form-check-label" for="inlineRadio2">
                    2
                  </label>
                </div>
                <div class="form-check form-check-inline">
                  <input
                    class="form-check-input"
                    type="radio"
                    name="inlineRadioOptions"
                    id="inlineRadio3"
                    value="option3"
                    disabled
                  />
                  <label class="form-check-label" for="inlineRadio3">
                    3 (disabled)
                  </label>
                </div>
              </div>
            </>
          ))}
        </CRow>

        <div className="d-flex mt-4 nowrap-cell">
          <div className="d-flex flex-row">
            <label className="pt-1">Grupo:</label>
            <CInput className="mr-2 ml-2" placeholder="590" />
          </div>
          <div className="d-flex flex-row">
            <label className="pt-1">Cota:</label>
            <CInput className="mr-2 ml-2" placeholder="1890" />
          </div>
          <div className="d-flex flex-row">
            <label className="pt-1">Nr. Contrato:</label>
            <CInput className="mr-2 ml-2" placeholder="1890 590 0" />
          </div>
        </div>

        <div className="row mt-3 px-3">
          <div className="col-md-6 m-0 p-1">
            <CLabel>Selecione as Parcelas Vencidas:</CLabel>
            <CCard>
              <CCardBody>
                <TableDatacobTermo
                  tableData={tableData}
                  handleTableDataChange={handleTableDataChange}
                />
              </CCardBody>
            </CCard>
          </div>
          <div className="col-md-6 m-0 p-1">
            <CLabel>Selecione as Parcelas Vincendas:</CLabel>
            <CCard>
              <CCardBody>
                <TableDatacobTermo
                  tableData={tableData}
                  handleTableDataChange={handleTableDataChange}
                />
              </CCardBody>
            </CCard>
          </div>
        </div>

        <div className="mt-0 nowrap-cell">
          <div className="row pr-4 mb-3">
            <div className="col-md-4">
              <label className="pt-1">Valor Total de parcelas vencidas:</label>
              <CInput className="mr-2 ml-2" />
            </div>
            <div className="col-md-4">
              <label className="pt-1">Multa e Juros:</label>
              <CInput className="mr-2 ml-2" />
            </div>
            <div className="col-md-4">
              <label className="pt-1">
                Diferença de parcelas (pagas a menor):
              </label>
              <CInput className="mr-2 ml-2" />
            </div>
          </div>
          <div className="row pr-4 mt-3 mb-3">
            <div className="col-md-4">
              <label className="pt-1">Valor de parcelas vincendas:</label>
              <CInput className="mr-2 ml-2" />
            </div>
            <div className="col-md-4">
              <label className="pt-1">Honorários advocatícios:</label>
              <CInput className="mr-2 ml-2" />
            </div>
            <div className="col-md-4">
              <label className="pt-1">Custas:</label>
              <CInput className="mr-2 ml-2" />
            </div>
          </div>
          <div className="row pr-4 mt-3 mb-3">
            <div className="col-md-3">
              <label className="pt-1">Total:</label>
              <CInput className="mr-2 ml-2" />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Qtd de Parcelas a serem geradas:</label>
              <CInput className="mr-2 ml-2" />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Data base:</label>
              <CInput className="mr-2 ml-2" />
            </div>
            <div className="col-md-3">
              <label className="pt-1">Valor acordado:</label>
              <CInput className="mr-2 ml-2" />
            </div>
          </div>
          <div className="row pr-4 mt-3 mb-3 justify-content-end">
            <CButton color="info" className="mr-2">
              Gerar Parcelas
            </CButton>
          </div>
        </div>

        <hr
          className="mt-3 mb-4"
          style={{ borderTop: "2px solid lightgray" }}
        />

        <CCard
          className="col-md-12"
          style={{ overflow: "auto", maxHeight: "200px" }}
        >
          <CCardBody>
            <div className="row">
              <div className="col-md-2">
                <label>Número de parcelas:</label>
              </div>
              <div className="col-md-3">
                <label>Data de vencimento:</label>
              </div>
              <div className="col-md-3">
                <label>Valor:</label>
              </div>
            </div>
            {Array.from({ length: 10 }, (_, i) => (
              <div className="row">
                <div className="col-md-2">
                  <span>{i + 1}</span>
                </div>
                {i > 0 && (
                  <div className="col-md-3 input-group-sm">
                    <input className="form-control mt-1" />
                  </div>
                )}
                {i === 0 && (
                  <div className="col-md-3">
                    <span className="pt-1">09/0{i + 5}/2025</span>
                  </div>
                )}
                <div className="col-md-3 input-group-sm">
                  <input className="form-control mt-1" />
                </div>
              </div>
            ))}
          </CCardBody>
        </CCard>
      </CModalBody>
      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default TermoJuridicoModal;
