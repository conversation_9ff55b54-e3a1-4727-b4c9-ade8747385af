import React, { useCallback, useEffect, useState } from "react";
import {
  CButton,
  CCard,
  CCardBody,
  CCol,
  CInput,
  CLabel,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
} from "@coreui/react";
import { TableDatacobTermo } from "./InstallmentTablesHandler";
import { getApi } from "src/reusable/functions";
import CardLoading from "src/reusable/CardLoading";
import Select from "react-select";
import {
  convertCurrencyToFloat,
  convertCurrencyToFloatDynamic,
  formatCurrency,
} from "src/reusable/helpers";

import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

// Componente wrapper para o DatePicker com estilos customizados
const CustomDatePicker = ({ selected, onChange, ...props }) => {
  const [popperPlacement, setPopperPlacement] = React.useState("top-start");
  const datePickerRef = React.useRef(null);

  // Função para determinar a melhor posição do calendário
  const determinePopperPlacement = React.useCallback(() => {
    if (datePickerRef.current) {
      const rect = datePickerRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      const spaceBelow = windowHeight - rect.bottom;
      const spaceAbove = rect.top;

      // Se há mais espaço acima ou se estamos na metade inferior da tela, abre para cima
      if (spaceAbove > spaceBelow || rect.top > windowHeight / 2) {
        setPopperPlacement("top-start");
      } else {
        setPopperPlacement("bottom-start");
      }
    }
  }, []);

  React.useEffect(() => {
    // Adiciona estilos customizados apenas uma vez
    const styleId = "custom-datepicker-styles";
    if (!document.getElementById(styleId)) {
      const styles = `
        .custom-datepicker-popper {
          z-index: 9999 !important;
        }

        .custom-datepicker-popper[data-placement^="top"] {
          margin-bottom: 5px !important;
        }

        .custom-datepicker-popper[data-placement^="bottom"] {
          margin-top: 5px !important;
        }

        .custom-datepicker-calendar {
          border: 1px solid #ced4da;
          border-radius: 0.375rem;
          box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
          font-family: inherit;
        }

        .react-datepicker-wrapper {
          width: 100% !important;
        }

        .react-datepicker__input-container {
          width: 100% !important;
        }

        .react-datepicker__input-container input {
          width: 100% !important;
          height: 38px !important;
          padding: 0.375rem 0.75rem !important;
          font-size: 0.875rem !important;
          line-height: 1.5 !important;
          border: 1px solid #ced4da !important;
          border-radius: 0.375rem !important;
          box-sizing: border-box !important;
          overflow: visible !important;
          text-overflow: clip !important;
          white-space: nowrap !important;
          min-width: 0 !important;
        }

        .react-datepicker__input-container input:focus {
          border-color: #80bdff !important;
          outline: 0 !important;
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
        }

        /* Garante que containers pais não cortem o conteúdo */
        .col-md-4:has(.react-datepicker-wrapper) {
          overflow: visible !important;
        }

        .mr-2.ml-2:has(.react-datepicker-wrapper) {
          overflow: visible !important;
        }

        .custom-datepicker-calendar .react-datepicker__header {
          background-color: #007bff;
          border-bottom: 1px solid #007bff;
          border-radius: 0.375rem 0.375rem 0 0;
        }

        .custom-datepicker-calendar .react-datepicker__current-month {
          color: white;
          font-weight: 500;
        }

        .custom-datepicker-calendar .react-datepicker__day-name {
          color: white;
          font-weight: 500;
        }

        .custom-datepicker-calendar .react-datepicker__day:hover {
          background-color: #e9ecef;
          border-radius: 0.25rem;
        }

        .custom-datepicker-calendar .react-datepicker__day--selected {
          background-color: #007bff;
          color: white;
          border-radius: 0.25rem;
        }

        .custom-datepicker-calendar .react-datepicker__day--keyboard-selected {
          background-color: #0056b3;
          color: white;
          border-radius: 0.25rem;
        }

        .custom-datepicker-calendar .react-datepicker__day--today {
          background-color: #ffc107;
          color: #212529;
          border-radius: 0.25rem;
          font-weight: 500;
        }

        .custom-datepicker-calendar .react-datepicker__navigation {
          top: 13px;
        }

        .custom-datepicker-calendar .react-datepicker__navigation--previous {
          border-right-color: white;
        }

        .custom-datepicker-calendar .react-datepicker__navigation--next {
          border-left-color: white;
        }
      `;

      const styleSheet = document.createElement("style");
      styleSheet.id = styleId;
      styleSheet.innerText = styles;
      document.head.appendChild(styleSheet);
    }
  }, []);

  return (
    <div
      ref={datePickerRef}
      style={{ width: "100%", overflow: "visible", position: "relative" }}
    >
      <ReactDatePicker
        selected={selected}
        onChange={onChange}
        className="form-control"
        dateFormat="dd/MM/yyyy"
        placeholderText="Selecione uma data"
        showPopperArrow={false}
        popperClassName="custom-datepicker-popper"
        calendarClassName="custom-datepicker-calendar"
        wrapperClassName="w-100"
        onKeyDown={(e) => e.preventDefault()}
        popperPlacement={popperPlacement}
        onCalendarOpen={determinePopperPlacement}
        {...props}
      />
    </div>
  );
};

const TermoJuridicoModal = ({ isOpen, onClose, datacobData }) => {
  const handleClose = () => {
    onClose();
  };

  const [tableDataVencidas, setTableDataVencidas] = useState([]);
  const [tableDataVincendas, setTableDataVincendas] = useState([]);
  const [grupoCotaContrato, setGrupoCotaContrato] = useState("");

  const handleTableDataVencidasChange = (tableData) => {
    setTableDataVencidas(tableData);
  };
  const handleTableDataVincendasChange = (tableData) => {
    setTableDataVincendas(tableData);
  };

  const [loading, setLoading] = useState(false);
  const [tipoTermo, setTipoTermo] = useState([]);
  const asyncLoadFunc = useCallback(async () => {
    setLoading(true);
    await Promise.all([getTipoTermo()]);
    await getTipoTermo();
    setLoading(false);
  }, []);

  const getTipoTermo = async () => {
    try {
      const response = await getApi({}, "getTermoJuridico");
      if (response && response.length > 0) {
        setTipoTermo(response);
      } else {
        setTipoTermo([]);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    asyncLoadFunc();
    return () => {
      setTableDataVencidas([]);
      setTableDataVincendas([]);
    };
  }, [asyncLoadFunc]);

  const [valorVencidas, setValorVencidas] = useState(0);
  const [valorVincendas, setValorVincendas] = useState(0);
  const [multaJuros, setMultaJuros] = useState(0);
  const [diferencaParcelas, setDiferencaParcelas] = useState(0);
  const [honorarios, setHonorarios] = useState(0);
  const [custas, setCustas] = useState(0);
  const [total, setTotal] = useState(0);
  const [qtdParcelas, setQtdParcelas] = useState(0);
  const [dataBase, setDataBase] = useState(new Date());
  const [valorAcordado, setValorAcordado] = useState(0);

  useEffect(() => {
    const vencidas = tableDataVencidas.filter((x) => x.parcelaSelecionada);
    const vincendas = tableDataVincendas.filter((x) => x.parcelaSelecionada);
    setValorVencidas(
      vencidas?.reduce((acum, item) => acum + item.vlOriginal, 0) ?? 0
    );
    setValorVincendas(
      vincendas?.reduce((acum, item) => acum + item.vlOriginal, 0) ?? 0
    );
    setMultaJuros(
      vencidas?.reduce(
        (acum, item) => acum + item.vlMultaNegociado + item.vlJurosNegociado,
        0
      ) ??
        0 +
          vincendas?.reduce(
            (acum, item) =>
              acum + item.vlMultaNegociado + item.vlJurosNegociado,
            0
          ) ??
        0
    );
    setHonorarios(
      vencidas?.reduce((acum, item) => acum + item.vlHoNegociado, 0) ??
        0 + vincendas?.reduce((acum, item) => acum + item.vlHoNegociado, 0) ??
        0
    );
    setCustas(
      vencidas?.reduce(
        (acum, item) =>
          acum +
          item.vlDespesasNegociado +
          item.vlNotificacaoNegociado +
          item.vlTarifaNegociado,
        0
      ) ??
        0 +
          vincendas?.reduce(
            (acum, item) =>
              acum +
              item.vlDespesasNegociado +
              item.vlNotificacaoNegociado +
              item.vlTarifaNegociado,
            0
          ) ??
        0
    );
    return () => {
      setValorVencidas(0);
      setValorVincendas(0);
      setMultaJuros(0);
      setDiferencaParcelas(0);
      setHonorarios(0);
      setCustas(0);
    };
  }, [tableDataVincendas, tableDataVencidas]);

  useEffect(() => {
    setTotal(
      valorVencidas +
        valorVincendas +
        multaJuros +
        diferencaParcelas +
        honorarios +
        custas
    );
    return () => {
      setTotal(0);
    };
  }, [
    valorVencidas,
    valorVincendas,
    multaJuros,
    diferencaParcelas,
    honorarios,
    custas,
    total,
  ]);

  const handleSelect = (option) => {
    setTableDataVencidas(option.parcelas.filter((x) => x.atraso > 0) ?? []);
    setTableDataVincendas(option.parcelas.filter((x) => x.atraso <= 0) ?? []);
    setGrupoCotaContrato(option.nrContrato);
  };

  const handleQntParcelasChange = (e) => {
    if (!/^\d*$/.test(e.target.value)) return;
    const value = parseInt(e.target.value);
    setQtdParcelas(isNaN(value) ? 0 : value);
  };

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      className="custom-modal modal-xxl"
      centered
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Dados para solicitar Termos Jurídicos:</h5>
      </CModalHeader>
      {loading && (
        <CModalBody style={{ minHeight: "470px" }}>
          <CardLoading />{" "}
        </CModalBody>
      )}
      {!loading && (
        <CModalBody>
          <div className="mb-4">
            <h6 className="mb-3">Selecione o tipo de termo jurídico:</h6>
            <div className="row px-4">
              {tipoTermo.map((termo, index) => (
                <div key={index} className="col-md-6 col-lg-4 mb-3">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="radio"
                      name="termoRadio"
                      id={`termoRadio${index}`}
                      value={termo.id}
                    />
                    <label
                      className="form-check-label"
                      htmlFor={`termoRadio${index}`}
                      style={{
                        fontSize: "13px",
                        lineHeight: "1.3",
                        cursor: "pointer",
                        fontWeight: "500",
                      }}
                    >
                      {termo.nome}
                    </label>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="row mt-4">
            <div className="col-md-4">
              <label className="pt-1">Selecione o Contrato:</label>
              <Select
                className="mr-2 ml-2"
                options={datacobData}
                getOptionLabel={(option) => option?.nrContrato}
                getOptionValue={(option) => option?.nrContrato}
                onChange={(option) => handleSelect(option)}
                placeholder="Selecione"
              />
            </div>
            <div className="col-md-4">
              <label className="pt-1">
                Exibição do Contrato no Termo Jurídico:
              </label>
              <CInput
                className="mr-2 ml-2"
                value={grupoCotaContrato}
                onChange={(e) => setGrupoCotaContrato(e.target.value)}
                disabled={
                  grupoCotaContrato === "" ||
                  grupoCotaContrato === null ||
                  grupoCotaContrato === undefined
                }
              />
            </div>
          </div>

          <div className="row mt-3 px-3">
            <div className="col-md-6 m-0 p-1">
              <CLabel>Selecione as Parcelas Vencidas:</CLabel>
              <CCard>
                <CCardBody>
                  <TableDatacobTermo
                    tableData={tableDataVencidas}
                    handleTableDataChange={handleTableDataVencidasChange}
                  />
                </CCardBody>
              </CCard>
            </div>
            <div className="col-md-6 m-0 p-1">
              <CLabel>Selecione as Parcelas Vincendas:</CLabel>
              <CCard>
                <CCardBody>
                  <TableDatacobTermo
                    tableData={tableDataVincendas}
                    handleTableDataChange={handleTableDataVincendasChange}
                  />
                </CCardBody>
              </CCard>
            </div>
          </div>

          <div className="mt-0 nowrap-cell">
            <div className="row pr-4 mb-3">
              <div className="col-md-4">
                <label className="pt-1">
                  Valor Total de parcelas vencidas:
                </label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(valorVencidas, false)}
                  readOnly
                />
              </div>
              <div className="col-md-4">
                <label className="pt-1">Valor de parcelas vincendas:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(valorVincendas, false)}
                  readOnly
                />
              </div>
              <div className="col-md-4">
                <label className="pt-1">
                  Diferença de parcelas (pagas a menor):
                </label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(diferencaParcelas, false)}
                  onChange={(e) =>
                    setDiferencaParcelas(
                      convertCurrencyToFloatDynamic(e.target.value)
                    )
                  }
                />
              </div>
            </div>
            <div className="row pr-4 mt-3 mb-3">
              <div className="col-md-3">
                <label className="pt-1">Honorários advocatícios:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(honorarios, false)}
                  onChange={(e) =>
                    setHonorarios(convertCurrencyToFloatDynamic(e.target.value))
                  }
                />
              </div>
              <div className="col-md-3">
                <label className="pt-1">Multa e Juros:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(multaJuros, false)}
                  onChange={(e) =>
                    setMultaJuros(convertCurrencyToFloatDynamic(e.target.value))
                  }
                />
              </div>
              <div className="col-md-3">
                <label className="pt-1">Custas:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(custas, false)}
                  onChange={(e) =>
                    setCustas(convertCurrencyToFloatDynamic(e.target.value))
                  }
                />
              </div>
              <div className="col-md-3">
                <label className="pt-1">Total:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(total, false)}
                  readOnly
                />
              </div>
            </div>
            <div className="row pr-4 mt-3 mb-3">
              <div className="col-md-4">
                <label className="pt-1">Qtd de Parcelas a serem geradas:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={qtdParcelas}
                  onChange={handleQntParcelasChange}
                />
              </div>
              <div className="col-md-4">
                <label className="pt-1">Data base:</label>
                <div className="mr-2 ml-2">
                  <CustomDatePicker
                    selected={dataBase}
                    onChange={(e) => setDataBase(e)}
                    minDate={new Date()}
                  />
                </div>
              </div>
              <div className="col-md-4">
                <label className="pt-1">Valor acordado:</label>
                <CInput
                  className="mr-2 ml-2"
                  value={formatCurrency(valorAcordado, false)}
                  onChange={(e) =>
                    setValorAcordado(
                      convertCurrencyToFloatDynamic(e.target.value)
                    )
                  }
                />
              </div>
            </div>
            <div className="row pr-4 mt-3 mb-3 justify-content-end">
              <CButton color="info" className="mr-2">
                Gerar Parcelas
              </CButton>
            </div>
          </div>

          <hr
            className="mt-3 mb-4"
            style={{ borderTop: "2px solid lightgray" }}
          />

          <CCard
            className="col-md-12"
            style={{ overflow: "auto", maxHeight: "200px" }}
          >
            <CCardBody>
              <div className="row">
                <div className="col-md-2">
                  <label>Número de parcelas:</label>
                </div>
                <div className="col-md-3">
                  <label>Data de vencimento:</label>
                </div>
                <div className="col-md-3">
                  <label>Valor:</label>
                </div>
              </div>
              {Array.from({ length: 0 }, (_, i) => (
                <div className="row" key={i}>
                  <div className="col-md-2">
                    <span>{i + 1}</span>
                  </div>
                  {i > 0 && (
                    <div className="col-md-3 input-group-sm">
                      <input
                        className="form-control mt-1"
                        value={""}
                        onChange={(e) => console.log(e.target.value)}
                      />
                    </div>
                  )}
                  {i === 0 && (
                    <div className="col-md-3">
                      <span className="pt-1">09/0{i + 5}/2025</span>
                    </div>
                  )}
                  <div className="col-md-3 input-group-sm">
                    <input
                      className="form-control mt-1"
                      value={""}
                      onChange={(e) => console.log(e.target.value)}
                    />
                  </div>
                </div>
              ))}
            </CCardBody>
          </CCard>
        </CModalBody>
      )}
      <CModalFooter>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default TermoJuridicoModal;
