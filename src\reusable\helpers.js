import { getURI } from "../config/apiConfig";

export function arredondarDecimaisParaCima(valor, casasDecimais) {
  // Verifica se o número de casas decimais é válido
  if (casasDecimais < 0) {
    throw new Error("Número de casas decimais não pode ser negativo");
  }

  // Calcula o fator de multiplicação com base no número de casas decimais
  const fator = Math.pow(10, casasDecimais);

  // Separa a parte inteira e a parte decimal
  const parteInteira = Math.floor(valor);
  const parteDecimal = valor - parteInteira;

  // Arredonda a parte decimal para cima
  const parteDecimalArredondada = Math.ceil(parteDecimal * fator) / fator;

  // Retorna a parte inteira somada à parte decimal arredondada
  return parteInteira + parteDecimalArredondada;
}

export function formatDate(dateString) {
  const date = new Date(dateString);
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const year = date.getFullYear();

  // Pad single-digit day or month with leading zero
  const formattedMonth = month.toString().padStart(2, "0");
  const formattedDay = day.toString().padStart(2, "0");

  return `${formattedDay}/${formattedMonth}/${year}`;
}

export function formatDateToGlobal(dateString) {
  const date = dateString.split("/");
  const month = date[1];
  const day = date[0];
  const year = date[2];

  // Pad single-digit day or month with leading zero
  const formattedMonth = month.toString().padStart(2, "0");
  const formattedDay = day.toString().padStart(2, "0");

  return `${year}-${formattedMonth}-${formattedDay}`;
}

export function formatDateGlobaltoSimplified(dateString) {
  const date = new Date(dateString);
  const month = date.getUTCMonth() + 1;
  const day = date.getDate();
  const year = date.getFullYear();

  // Pad single-digit day or month with leading zero
  const formattedMonth = month.toString().padStart(2, "0");
  const formattedDay = day.toString().padStart(2, "0");

  return `${year}-${formattedMonth}-${formattedDay}`;
}

export function formatDateTime(dateString) {
  const date = new Date(dateString);
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const year = date.getFullYear();
  const hour = date.getHours();
  const minutes = date.getMinutes();

  // Pad single-digit day or month with leading zero
  const formattedMonth = month.toString().padStart(2, "0");
  const formattedDay = day.toString().padStart(2, "0");
  const formatHour = hour.toString().padStart(2, "0");
  const formatMinutes = minutes.toString().padStart(2, "0");

  return `${formattedDay}/${formattedMonth}/${year} ${formatHour}:${formatMinutes}`;
}

export function leftPad(str, length, char) {
  var padding = "";
  if (typeof char === "undefined") {
    char = " ";
  }

  while (padding.length < length - str.length) {
    padding += char;
  }

  return padding + str;
}

// export function formatThousands (value) {
//   return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
// };

export function formatThousands(value) {
  const roundedValue = parseFloat(value).toFixed(2); // Ensure exactly two decimal places
  const parts = roundedValue.split(".");
  const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ".");
  const decimalPart = parts[1];
  return `${integerPart},${decimalPart}`;
}

export function formatDocument(value) {
  if (value.length === 14) {
    //CNPJ
    return value.replace(
      /(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/,
      "$1.$2.$3/$4-$5"
    );
  }
  //CPF
  else return value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
}

export function formatFone(value) {
  return value.replace(/(\d{5})(\d{4})/, "$1-$2");
}

export const formatCurrency = (value, showTagCurrency = true) => {
  try {
    const formattedValue = value.toLocaleString("pt-BR", {
      style: "currency",
      currency: "BRL",
    });
    if (showTagCurrency) return formattedValue;
    return formattedValue.replace("R$", "").trim();
  } catch {
    return value;
  }
};

export const floatToPerc = (value, numberOfDecimals = 2) => {
  try {
    let formattedValue = value.toFixed(numberOfDecimals);

    return formattedValue.replace(".", ",");
  } catch {
    return value;
  }
};

export const convertCurrencyToFloat = (currencyString) => {
  try {
    let cleanedString = currencyString.replace(".", "");
    cleanedString = cleanedString.replace(/[^\d.,]/g, "");
    cleanedString = cleanedString.replace(",", "."); // Replace the first occurrence of ',' with a period ('.') to handle decimal values
    const floatValue = parseFloat(cleanedString);
    return floatValue;
  } catch {
    return null;
  }
};

export const convertCurrencyToFloatDynamic = (currencyString) => {
  try {
    // Remove caracteres que não são dígitos
    const sanitizedValue = currencyString.replace(/[^\d]/g, "");
    // Formata o valor enquanto o usuário digita
    const formattedValue = formatarParaMoeda(sanitizedValue);
    const formattedValueF = formattedValue.replace(/\./g, "").replace(",", ".");
    const floatValue = parseFloat(formattedValueF);
    return isNaN(floatValue) ? 0 : floatValue;
  } catch {
    return 0;
  }
};

export const calculateDaysDifference = (data1, data2) => {
  const diffMilliseconds = Math.abs(data1 - data2);
  const diffDays = Math.floor(diffMilliseconds / (1000 * 60 * 60 * 24));
  return diffDays;
};

export function formatCodigoNewcon(number) {
  if (typeof number !== "number" || isNaN(number)) {
    return null;
  }
  const numberString = number.toString().padStart(4, "0");
  const formattedNumber =
    numberString.slice(0, -1) + "-" + numberString.slice(-1);

  return formattedNumber;
}

export function formatBuscaDocumento(inputString) {
  const formattedString = inputString.replace(/[.\-/]/g, "");
  return formattedString;
}

export const isEmailValid = (email) => {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  const listMail = email.split(";").map((e) => e.trim());

  for (const mail of listMail) {
    if (!emailRegex.test(mail)) {
      return false;
    }
  }
  return true;
};

export const formatFoneWithoutDdd = (inputValue) => {
  inputValue = inputValue.replace(/\D/g, "");
  let formattedValue = "";

  if (inputValue.length > 0) {
    formattedValue = inputValue.substring(0, inputValue.length === 9 ? 5 : 4);

    if (inputValue.length > 4) {
      formattedValue +=
        "-" +
        inputValue.substring(
          inputValue.length === 9 ? 5 : 4,
          inputValue.length
        );
    }
  }

  return formattedValue;
};

export const extractDDD = (telefone) => {
  // Expressão regular para encontrar o DDD
  var regex = /^\(?(\d{2})\)?[-.\s]?\d{4,5}[-.\s]?\d{4}$/;

  // Executar a expressão regular no número de telefone
  var matches = regex.exec(telefone);

  // Se houver correspondência, retornar o DDD
  if (matches && matches.length > 1) {
    return matches[1];
  } else {
    // Se não houver correspondência, retornar null ou uma mensagem de erro, conforme desejado
    return null;
  }
};
export const removeDDDfromPhone = (telefone) => {
  if (telefone.indexOf(" ") === -1) {
    if (telefone.startsWith("0")) return telefone.substring(3);

    return telefone.substring(2);
  }
  // Expressão regular para encontrar o número de telefone sem DDD
  var regex = /(?:\(?(\d{2})\)?\s)?(?:[-.\s])?(\d{4,5}[-.\s]?\d{4})/;

  // Executar a expressão regular no número de telefone
  var matches = regex.exec(telefone);

  // Se houver correspondência, retornar o número de telefone sem DDD
  if (matches && matches.length > 2) {
    return matches[2];
  } else {
    // Se não houver correspondência, retornar null ou uma mensagem de erro, conforme desejado
    return null;
  }
};

export const strEmptyOrNull = (str) => {
  if (str === null || str === undefined || str === "") {
    return null;
  }
  return str;
};

export const formatarParaMoeda = (valor) => {
  let valorNumerico = valor.replace(/\D/g, "");
  valorNumerico = (parseInt(valorNumerico, 10) / 100).toFixed(2);
  valorNumerico = valorNumerico.replace(".", ",");

  // Dividindo a string em parte inteira e decimal
  let partes = valorNumerico.split(",");
  let parteInteira = partes[0];
  let parteDecimal = partes[1];

  // Adicionando os pontos como separadores de milhares
  let parteInteiraFormatada = parteInteira.replace(
    /\B(?=(\d{3})+(?!\d))/g,
    "."
  );

  // Reconstruindo o valor formatado
  valorNumerico = parteInteiraFormatada + "," + parteDecimal;

  return valorNumerico;
};

export const arraysAreEqual = (arr1, arr2) => {
  if (arr1.length !== arr2.length) {
    return false;
  }
  for (let i = 0; i < arr1.length; i++) {
    if (arr1[i] !== arr2[i]) {
      return false;
    }
  }
  return true;
};

export const roundFloat = (val) => {
  return Math.round(val * 100) / 100;
};

export const toTitleCase = (str) => {
  if (!str) return str;

  return str
    .toLowerCase()
    .split(" ")
    .map((word) => {
      // Palavras que devem permanecer em minúsculas (preposições, artigos, etc.)
      const lowercaseWords = [
        "de",
        "da",
        "do",
        "das",
        "dos",
        "e",
        "em",
        "na",
        "no",
        "nas",
        "nos",
        "a",
        "o",
        "as",
        "os",
        "para",
        "por",
        "com",
        "sem",
      ];

      // Se a palavra está na lista de palavras que devem ficar em minúsculas
      // e não é a primeira palavra da frase, mantém em minúsculas
      if (
        lowercaseWords.includes(word.toLowerCase()) &&
        str.split(" ").indexOf(word) !== 0
      ) {
        return word.toLowerCase();
      }

      // Capitaliza a primeira letra
      return word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(" ");
};

async function hashSHA256(text) {
  const encoder = new TextEncoder();
  const data = encoder.encode(text);
  const hashBuffer = await crypto.subtle.digest("SHA-256", data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map((b) => b.toString(16).padStart(2, "0")).join("");
}

export const postManager = async (token, value, type) => {
  try {
    const key = await hashSHA256(value);
    const response = await fetch(`${getURI()}/Manager/encrypt`, {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ value, type, key }),
    });

    if (response.ok) {
      return await response.json();
    } else {
      console.error("Erro:", response?.statusText);
    }
  } catch (error) {
    console.error("Erro no encrypt:", error);
  }
};
