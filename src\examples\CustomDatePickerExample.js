import React, { useState } from 'react';
import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

// Componente wrapper para o DatePicker com estilos customizados
const CustomDatePicker = ({ selected, onChange, ...props }) => {
  React.useEffect(() => {
    // Adiciona estilos customizados apenas uma vez
    const styleId = "custom-datepicker-styles";
    if (!document.getElementById(styleId)) {
      const styles = `
        .custom-datepicker-popper {
          z-index: 9999 !important;
        }
        
        .custom-datepicker-calendar {
          border: 1px solid #ced4da;
          border-radius: 0.375rem;
          box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
          font-family: inherit;
        }
        
        .custom-datepicker-calendar .react-datepicker__header {
          background-color: #007bff;
          border-bottom: 1px solid #007bff;
          border-radius: 0.375rem 0.375rem 0 0;
        }
        
        .custom-datepicker-calendar .react-datepicker__current-month {
          color: white;
          font-weight: 500;
        }
        
        .custom-datepicker-calendar .react-datepicker__day-name {
          color: white;
          font-weight: 500;
        }
        
        .custom-datepicker-calendar .react-datepicker__day:hover {
          background-color: #e9ecef;
          border-radius: 0.25rem;
        }
        
        .custom-datepicker-calendar .react-datepicker__day--selected {
          background-color: #007bff;
          color: white;
          border-radius: 0.25rem;
        }
        
        .custom-datepicker-calendar .react-datepicker__day--keyboard-selected {
          background-color: #0056b3;
          color: white;
          border-radius: 0.25rem;
        }
        
        .custom-datepicker-calendar .react-datepicker__day--today {
          background-color: #ffc107;
          color: #212529;
          border-radius: 0.25rem;
          font-weight: 500;
        }
        
        .custom-datepicker-calendar .react-datepicker__navigation {
          top: 13px;
        }
        
        .custom-datepicker-calendar .react-datepicker__navigation--previous {
          border-right-color: white;
        }
        
        .custom-datepicker-calendar .react-datepicker__navigation--next {
          border-left-color: white;
        }
      `;
      
      const styleSheet = document.createElement("style");
      styleSheet.id = styleId;
      styleSheet.innerText = styles;
      document.head.appendChild(styleSheet);
    }
  }, []);

  return (
    <ReactDatePicker
      selected={selected}
      onChange={onChange}
      className="form-control"
      dateFormat="dd/MM/yyyy"
      placeholderText="Selecione uma data"
      showPopperArrow={false}
      popperClassName="custom-datepicker-popper"
      calendarClassName="custom-datepicker-calendar"
      wrapperClassName="w-100"
      onKeyDown={(e) => e.preventDefault()}
      {...props}
    />
  );
};

const CustomDatePickerExample = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [minDate, setMinDate] = useState(new Date());

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>Exemplo do CustomDatePicker Melhorado</h2>
      
      <div style={{ marginBottom: '30px' }}>
        <h3>Melhorias Implementadas:</h3>
        <ul style={{ lineHeight: '1.6' }}>
          <li><strong>Visual Consistente:</strong> Alinhado com o design do Bootstrap</li>
          <li><strong>Header Azul:</strong> Cabeçalho com cor primária (#007bff)</li>
          <li><strong>Hover Effects:</strong> Efeitos visuais ao passar o mouse</li>
          <li><strong>Data Atual Destacada:</strong> Dia atual em amarelo</li>
          <li><strong>Seleção Clara:</strong> Data selecionada bem visível</li>
          <li><strong>Z-index Alto:</strong> Sempre aparece sobre outros elementos</li>
          <li><strong>Navegação Melhorada:</strong> Setas de navegação mais visíveis</li>
          <li><strong>Bordas Arredondadas:</strong> Design moderno</li>
          <li><strong>Placeholder Text:</strong> Texto de ajuda para o usuário</li>
          <li><strong>Prevenção de Digitação:</strong> Apenas seleção via calendário</li>
        </ul>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h4>Exemplo de Uso:</h4>
        <div style={{ maxWidth: '300px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            Data Base:
          </label>
          <CustomDatePicker
            selected={selectedDate}
            onChange={(date) => setSelectedDate(date)}
            minDate={minDate}
          />
        </div>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
        <h4>Data Selecionada:</h4>
        <p style={{ fontSize: '16px', color: '#007bff', fontWeight: 'bold' }}>
          {selectedDate ? selectedDate.toLocaleDateString('pt-BR') : 'Nenhuma data selecionada'}
        </p>
      </div>

      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#e9ecef', borderRadius: '5px' }}>
        <h4>Como usar no seu componente:</h4>
        <pre style={{ backgroundColor: '#fff', padding: '10px', borderRadius: '3px', overflow: 'auto' }}>
{`// Import do componente
import CustomDatePicker from './CustomDatePicker';

// No seu componente
const [dataBase, setDataBase] = useState(new Date());

// No JSX
<div className="col-md-4">
  <label className="pt-1">Data base:</label>
  <div className="mr-2 ml-2">
    <CustomDatePicker
      selected={dataBase}
      onChange={(e) => setDataBase(e)}
      minDate={new Date()}
    />
  </div>
</div>`}
        </pre>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#d1ecf1', borderRadius: '5px' }}>
        <h4>Propriedades Disponíveis:</h4>
        <ul>
          <li><code>selected</code>: Data selecionada</li>
          <li><code>onChange</code>: Função chamada quando a data muda</li>
          <li><code>minDate</code>: Data mínima selecionável</li>
          <li><code>maxDate</code>: Data máxima selecionável</li>
          <li><code>disabled</code>: Desabilita o componente</li>
          <li><code>...props</code>: Todas as outras propriedades do ReactDatePicker</li>
        </ul>
      </div>
    </div>
  );
};

export default CustomDatePickerExample;
