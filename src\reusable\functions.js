import { AXIOS_POST, DELETE_DATA, GET_DATA, POST_DATA, PUT_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import { formatDocument } from "./helpers";

export async function GET_FINANCIADO(item) {
  const contratosAbertos = await getContratosAbertos(
    item.cpfCnpj,
    item.id_Agrupamento
  );

  localStorage.setItem("cnscCotas", null);
  localStorage.setItem("newcon", null);

  localStorage.removeItem("clientData");
  localStorage.removeItem("contratosAtivos");
  localStorage.removeItem("saldoAcumulado");
  localStorage.removeItem("agenda");
  localStorage.removeItem("estruturaVendas");
  localStorage.removeItem("bens");

  localStorage.setItem("contratosAbertos", JSON.stringify(contratosAbertos));
  localStorage.setItem("financiadoData", JSON.stringify(item));
  return item;
}

async function getDadosAvalistas(id_Agrupamento) {
  const data = { IdAgrupamento: id_Agrupamento };
  const avalistas = await GET_DATA("Datacob/DadosAvalistas", data);
  return avalistas;
}

export async function getAcordos(id_Agrupamento, numeroContrato) {
  const data = { IdAgrupamento: id_Agrupamento, numeroContrato: numeroContrato };
  const acordos = await GET_DATA("Datacob/Acordos", data);
  return acordos;
}

export async function getAcordosManuais(id_Contrato, numeroContrato) {
  const data = { idContrato: id_Contrato, numeroContrato: numeroContrato };
  const acordos = await GET_DATA("AcordoManual/GetAcordosManuais", data);
  return acordos;
}



async function getContratosAbertos(cpfCnpj, id_Agrupamento) {
  const data = { Cpfcnpj: cpfCnpj, IdAgrupamento: id_Agrupamento };
  const contratosAbertos = await GET_DATA("Datacob/GrupoContratoAberto", data);
  return contratosAbertos;
}

async function getHistoricoAtendimento(Id_Agrupamento) {
  const data = { Id_Agrupamento: Id_Agrupamento };
  const historicoAtendimento = await GET_DATA(
    "Datacob/HistoricoAtendimento",
    data
  );
  return historicoAtendimento;
}

export async function getProcessos(cpfCnpj) {
  const formatedDocument = formatDocument(cpfCnpj);
  const data = { IdEntidade: formatedDocument };
  const processos = await GET_DATA("Projuris/Processos", data);
  localStorage.setItem("processos", JSON.stringify(processos));
  return processos;
}

export async function getEntidades(cpfCnpj) {
  const formatedDocument = formatDocument(cpfCnpj);
  const data = { document: formatedDocument };
  try {
    const response = await GET_DATA("Projuris/Entidades", data);
    return response;
  } catch (error) {
    return error;
  }
}

export async function getCNSCotas(payload, endpoint) {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(getURI(endpoint), payload, true);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
}

export async function getNewconDados(payload, endpoint = "") {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(
        getURI(endpoint),
        null,
        true,
        true,
        payload
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
}

export async function getDadosFinanciado(id_Financiado, numero_Contrato) {
  if (!id_Financiado) return;
  
  const data = { IdFinanciado: id_Financiado, numeroContrato: numero_Contrato };
  const dadosFinanciado = await GET_DATA(
    "Datacob/DadosFinanciadoAdjacente",
    data
  );
  return dadosFinanciado;
}

export async function GET_EXTRA(item) {
  const avalistasTerceiros = await getDadosAvalistas(item.id_Agrupamento);

  const acordos = await getAcordos(item.id_Agrupamento, item?.numero_Contrato);

  localStorage.setItem(
    "avalistasTerceiros",
    JSON.stringify(avalistasTerceiros)
  );
  localStorage.setItem("acordos", JSON.stringify(acordos));
}

export async function getTiposEmails() {
  const emails = await GET_DATA("Datacob/TipoEmail");
  return emails;
}
export async function getTiposEndereco() {
  const end = await GET_DATA("Datacob/TipoEndereco");
  return end;
}
export async function getTiposTelefone() {
  const tele = await GET_DATA("Datacob/TipoTelefone");
  return tele;
}
export async function getTiposParcelas() {
  const parc = await GET_DATA("Datacob/TipoParcela");
  return parc;
}
export async function getTipoReferencia() {
  const refer = await GET_DATA("Datacob/TipoReferencia");
  return refer;
}

export async function GET_TIPOS() {
  const tiposEmail = await getTiposEmails();
  const tiposEndereco = await getTiposEndereco();
  const tiposTelefone = await getTiposTelefone();
  const tiposParcelas = await getTiposParcelas();
  const tiposReferencias = await getTipoReferencia();

  const tipos = {
    tiposEmail,
    tiposEndereco,
    tiposParcelas,
    tiposTelefone,
    tiposReferencias,
  };
  localStorage.setItem("tiposOpcoes", JSON.stringify(tipos));
}

export async function GET_ClientData(item) {
  if (!item) return null;
  const clientData = await getDadosFinanciado(item?.id_Financiado, item?.numero_Contrato);
  if (clientData) {
    localStorage.setItem("clientData", JSON.stringify(clientData));
  }
  return clientData;
}

export async function getCidades() {
  const cidades = await GET_DATA("Datacob/Cidades");
  return cidades;
}

export function capitalizeAndSpace(str) {
  return str.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
}

export function formatarTelefone(telefone) {
  // Remove espaços no começo e no final
  let numeros = telefone.trim();

  // Remove caracteres não numéricos
  numeros = numeros.replace(/\D/g, "");

  // Verifica se começa com '55' e se tem mais de 11 dígitos
  if (numeros.startsWith("55") && numeros.length > 11) {
    numeros = numeros.substring(2);
  }

  // Formata o número com base no seu comprimento
  if (numeros.length === 10) {
    // Formato xx xxxx-xxxx
    return numeros.replace(/(\d{2})(\d{4})(\d{4})/, "$1 $2-$3");
  } else if (numeros.length === 11) {
    // Formato xx xxxxx-xxx
    return numeros.replace(/(\d{2})(\d{5})(\d{4})/, "$1 $2-$3");
  } else {
    // Retorna a string original se não corresponder a nenhum formato
    return telefone;
  }
}

export const getApi = async (payload, endpoint) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(getURI(endpoint), payload, true);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

export const getApiInline = async (payload, endpoint) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await GET_DATA(
        getURI(endpoint),
        null,
        true,
        true,
        `/${payload}`
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

export const deleteApiInline = async (payload, endpoint) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await DELETE_DATA(
        getURI(endpoint),
        null,
        true,
        true,
        `/${payload}`
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

export const deleteApi = async (payload, endpoint) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await DELETE_DATA(
        getURI(endpoint),
        null,
        true,
        true,
        payload
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

export const postApi = async (payload, endpoint) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await POST_DATA(
        getURI(endpoint),
        payload,
        true,
        true,
        ""
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

export const putApi = async (payload, endpoint) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await PUT_DATA(
        getURI(endpoint),
        payload,
        true,
        true,
        ""
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

export const postAxios = async (payload, endpoint) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await AXIOS_POST(
        getURI(endpoint),
        payload,
        true,
        true,
        ""
      );
      resolve(response);
    } catch (error) {
      reject(error);
    }
  });
};

export const sendLocalStorage = async (arrayLocalStorage, id) => {
  for (const item of arrayLocalStorage) {
    await postApi(
      { id: id, localStorage: item },
      "postLogErrorFromAppLocalStorage"
    );
  }
};
