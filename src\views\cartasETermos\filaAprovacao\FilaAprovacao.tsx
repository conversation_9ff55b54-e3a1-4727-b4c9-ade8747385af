import {
  CButton,
  CCard,
  CCardBody,
  CCol,
  CDataTable,
  CRow,
} from "@coreui/react";
import { filter } from "core-js/core/array";
import { get, set } from "core-js/core/dict";
import { C } from "jssip";
import React, { useEffect, useState } from "react";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import AprovacaoModal from "./partials/aprovacaoModal.tsx";
import { ItemFilaAprovacaoType } from "../types/ItemFilaAprovacaoType.ts";
import { formatDate } from "src/reusable/helpers.js";

const FilaAprovacao = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [filaAprovacao, setFilaAprovacao] = useState([]);

  const [showAprovacaoModal, setShowAprovacaoModal] = useState(false);
  const [itemSelecionado, setItemSelecionado] =
    useState<ItemFilaAprovacaoType>(null);
  const [statusAprovacao, setStatusAprovacao] = useState<string>("");

  const getFilaAprovacaoCartasETermos = async () => {
    setIsLoading(true);
    await GetData(null, "filaAprovacaoCartasETermos")
      .then((resultado: ItemFilaAprovacaoType[]) => {
        if (resultado) {
          setFilaAprovacao(resultado);
        } else {
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  useEffect(() => {
    getFilaAprovacaoCartasETermos();
  }, []);

  const colums = [
    {
      key: "financiado",
      label: "Cliente",
    },
    {
      key: "idContrato",
      label: "Contrato",
    },
    {
      key: "operador",
      label: "Operador",
    },
    {
      key: "createdAt",
      label: "Data Criação",
      filter: false,
    },
    {
      key: "status",
      label: "Status",
      filter: false,
    },
    {
      key: "tipo",
      label: "Tipo",
      filter: false,
    },
    {
      key: "actions",
      label: "Ações",
      filter: false,
    },
  ];
  const IconButton = ({
    icon,
    color,
    titulo,
    onClick,
    disabled = false,
    className = "",
  }) => {
    return (
      <CButton
        title={titulo}
        className={className}
        style={{
          border: "solid 1px",
          borderColor: color,
          color: color,
          padding: "2px 4px",
        }}
        onClick={onClick}
        disabled={disabled}
      >
        <i className={icon} />
      </CButton>
    );
  };
  const handleClickAprovar = (item: ItemFilaAprovacaoType, status: string) => {
    setShowAprovacaoModal(true);
    setItemSelecionado(item);
    setStatusAprovacao(status);
  };
  const handleCloseAprovacaoModal = async () => {
    setShowAprovacaoModal(false);
    setItemSelecionado(null);
    setStatusAprovacao("");
    await getFilaAprovacaoCartasETermos();
  };
  const renderActions = (item) => {
    return (
      <td>
        <IconButton
          className="ml-2"
          icon={"cil-check"}
          color={"blue"}
          titulo={"Aprovar"}
          onClick={() => handleClickAprovar(item, "Aprovado")}
        />
        <IconButton
          className="ml-2"
          icon={"cil-pencil"}
          color={"orange"}
          titulo={"Editar"}
          onClick={() => {}}
        />
        <IconButton
          className="ml-2"
          icon={"cil-x-circle"}
          color={"red"}
          titulo={"Rejeitar"}
          onClick={() => handleClickAprovar(item, "Rejeitado")}
        />
        <IconButton
          className="ml-2"
          icon={"cil-arrow-thick-bottom"}
          color={"blue"}
          titulo={"Baixar Documento"}
          onClick={{}}
        />
      </td>
    );
  };
  return (
    <div>
      <CRow>
        <CCol>
          <h1>Fila de Aprovação de Documentos</h1>
        </CCol>
      </CRow>

      <CRow>
        <CCol>
          <CCard>
            <CCardBody>
              <CDataTable
                items={filaAprovacao}
                fields={colums}
                striped
                hover
                columnFilter
                scopedSlots={{
                  createdAt: (item) => <td>{formatDate(item.createdAt)}</td>,
                  actions: (item) => renderActions(item),
                }}
                itemsPerPage={15}
                pagination
                noItemsViewSlot={
                  <h5 className="text-center">Sem resultados para exibir.</h5>
                }
              />
            </CCardBody>
          </CCard>
        </CCol>
      </CRow>
      <CRow className={""}>
        <CCol className={"d-flex justify-content-end"}>
          <CButton color="danger" className={"mr-2"} onClick={() => {}}>
            Sair
          </CButton>
          <CButton color="success" onClick={() => {}}>
            Exportar Excel
          </CButton>
        </CCol>
      </CRow>
      <AprovacaoModal
        isOpen={showAprovacaoModal}
        item={itemSelecionado}
        statusAprovacao={statusAprovacao}
        onClose={() => {
          handleCloseAprovacaoModal();
        }}
      />
    </div>
  );
};

export default FilaAprovacao;
